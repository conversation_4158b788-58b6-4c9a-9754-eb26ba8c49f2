package com.example.demoziplock

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.graphics.withRotation
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.sin


class ZipperView : View {
    private var mBitmapLock: Bitmap? = null
    private var mBitmapBg: Bitmap? = null
    private var toothBitmap: Bitmap? = null

    private var mPaint: Paint? = null
    private var moveY = 0f
    private var yTouch = 0f

    private var isTouch = false
    private var INIT_HEIGHT_INT = 0f

    private var wSize = 0
    private var hSize = 0

    constructor(context: Context?) : super(context) {
        init(context)
    }

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(context)
    }

    private fun init(context: Context?) {
        mPaint = Paint()
        mPaint!!.isAntiAlias = true
        mPaint!!.isFilterBitmap = true

        mBitmapLock = BitmapFactory.decodeResource(resources, R.drawable.zipper)
        //mBitmapBg = BitmapFactory.decodeResource(resources, R.drawable.zipper_bg)
        toothBitmap = BitmapFactory.decodeResource(resources, R.drawable.tooth)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        wSize = w
        hSize = h
        INIT_HEIGHT_INT = 0f
        super.onSizeChanged(w, h, oldw, oldh)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // Draw background
        //canvas.drawBitmap(mBitmapBg!!, 0f, 0f, mPaint)

        if (isTouch && (yTouch - moveY) > 0) {
            val baseY = (yTouch - moveY)

            // Draw zipper teeth along curve
            drawZipperTeeth(canvas, baseY)

            // Draw lock head
            canvas.drawBitmap(
                mBitmapLock!!,
                wSize / 2f - mBitmapLock!!.width / 2f,
                baseY,
                mPaint
            )
        } else {
            // Draw zipper fully closed
            drawZipperTeeth(canvas, INIT_HEIGHT_INT)
            canvas.drawBitmap(
                mBitmapLock!!,
                wSize / 2f - mBitmapLock!!.width / 2f,
                INIT_HEIGHT_INT,
                mPaint
            )
        }
    }

    private fun drawZipperTeeth(canvas: Canvas, baseY: Float) {
        if (toothBitmap == null) return

        val spacing = 30f
        val centerX = wSize / 2f
        val toothWidth = toothBitmap!!.width

        var y = INIT_HEIGHT_INT
        while (y < baseY) {
            val progress = (y - INIT_HEIGHT_INT) / (baseY - INIT_HEIGHT_INT + 0.001f)
            val offset = (sin(progress * Math.PI) * 60f).toFloat() // biên độ cong

            val leftX = centerX - offset - toothWidth
            val rightX = centerX + offset

            canvas.drawBitmap(toothBitmap!!, leftX, y, mPaint)
            canvas.drawBitmap(toothBitmap!!, rightX, y, mPaint)
            y += spacing
        }
    }

    /*private fun drawZipperTeeth(canvas: Canvas, baseY: Float) {
        if (toothBitmap == null) return

        val spacing = 30f
        val centerX = wSize / 2f
        val toothWidth = toothBitmap!!.width
        val maxOffset = centerX - toothWidth - 10f // cách mép 10px

        var y = INIT_HEIGHT_INT
        while (y < baseY) {
            val progress = (y - INIT_HEIGHT_INT) / (baseY - INIT_HEIGHT_INT + 0.001f)

            // Offset theo parabol đối xứng, mở rộng từ giữa ra hai mép
            val p = 2 * progress - 1  // từ -1 → 0 → +1
            val parabola = 1f - (p * p) // từ 0 → 1 → 0
            val offset = maxOffset * parabola

            val leftX = centerX - offset - toothWidth
            val rightX = centerX + offset

            canvas.drawBitmap(toothBitmap!!, leftX, y, mPaint)
            canvas.drawBitmap(toothBitmap!!, rightX, y, mPaint)

            y += spacing
        }
    }*/

    override fun onTouchEvent(event: MotionEvent): Boolean {
        yTouch = event.getY()

        when (event.getAction()) {
            MotionEvent.ACTION_DOWN -> {
                isTouch = true
                //moveY = 0f
                invalidate()
            }

            MotionEvent.ACTION_MOVE -> {
                moveY = yTouch - event.y
                invalidate()
            }

            MotionEvent.ACTION_UP -> {
                isTouch = false
                //moveY = 0f
                invalidate()
            }
        }
        return true
    }
}
